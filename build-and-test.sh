#!/bin/bash

# ================================================================================
# 文件传输SDK简化构建和测试脚本
# ================================================================================

set -e  # 遇到错误立即退出

# ==================== 常量定义 ====================

# 脚本版本信息
readonly SCRIPT_VERSION="1.0.0"
readonly SCRIPT_NAME="文件传输SDK简化构建和测试脚本"

# 项目模块列表
readonly PROJECT_MODULES=(
    "file-transfer-server-sdk"
    "file-transfer-client-sdk"
    "file-transfer-client-demo"
)

# 需要单元测试的模块列表
readonly TEST_MODULES=(
    "file-transfer-server-sdk"
    "file-transfer-client-sdk"
)

# 演示模块列表（不执行单元测试，而是执行演示程序）
readonly DEMO_MODULES=(
    "file-transfer-client-demo"
)

# 独立服务模块
readonly STANDALONE_MODULE="file-transfer-server-standalone"

# 超时配置常量
readonly BUILD_TIMEOUT_SECONDS=600    # 构建超时时间（10分钟）
readonly TEST_TIMEOUT_SECONDS=1200    # 测试超时时间（20分钟）
readonly SERVER_STARTUP_TIMEOUT_SECONDS=30  # 服务器启动超时时间（30秒）
readonly SERVER_SHUTDOWN_TIMEOUT_SECONDS=15  # 服务器关闭超时时间（15秒）
readonly DEMO_TEST_TIMEOUT_SECONDS=300  # 演示测试超时时间（5分钟）

# 端口配置
readonly TEST_SERVER_PORT=49011  # 测试服务器端口

# 目录配置
readonly LOG_DIR="./logs"
readonly MAIN_LOG="$LOG_DIR/build-and-test-$(date +%Y%m%d_%H%M%S).log"

readonly SERVER_PID_FILE="$LOG_DIR/test-server.pid"

# 执行模式常量
readonly MODE_BUILD="build"
readonly MODE_BUILD_TEST="build-test"

# ==================== 颜色定义 ====================

readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_PURPLE='\033[0;35m'
readonly COLOR_CYAN='\033[0;36m'
readonly COLOR_NC='\033[0m' # No Color

# ==================== 日志函数 ====================

# 信息日志 - 记录一般信息
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_BLUE}[INFO]${COLOR_NC} ${timestamp} - ${message}"
    echo "[INFO] ${timestamp} - ${message}" >> "$MAIN_LOG"
}

# 成功日志 - 记录成功操作
log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_NC} ${timestamp} - ${message}"
    echo "[SUCCESS] ${timestamp} - ${message}" >> "$MAIN_LOG"
}

# 警告日志 - 记录警告信息
log_warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_NC} ${timestamp} - ${message}"
    echo "[WARNING] ${timestamp} - ${message}" >> "$MAIN_LOG"
}

# 错误日志 - 记录错误信息
log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_RED}[ERROR]${COLOR_NC} ${timestamp} - ${message}"
    echo "[ERROR] ${timestamp} - ${message}" >> "$MAIN_LOG"
}

# 步骤日志 - 记录主要执行步骤，便于跟踪进度
log_step() {
    local step_number="$1"
    local step_name="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_PURPLE}[STEP ${step_number}]${COLOR_NC} ${timestamp} - ${step_name}"
    echo "[STEP ${step_number}] ${timestamp} - ${step_name}" >> "$MAIN_LOG"
    echo "========================================" >> "$MAIN_LOG"
}

# 测试阶段日志 - 专门用于标记测试阶段，便于问题定位
log_test_phase() {
    local phase_name="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${COLOR_CYAN}[TEST_PHASE]${COLOR_NC} ${timestamp} - ${phase_name}"
    echo "[TEST_PHASE] ${timestamp} - ${phase_name}" >> "$MAIN_LOG"
    echo "----------------------------------------" >> "$MAIN_LOG"
}

# ==================== 工具函数 ====================

# 初始化日志目录和文件
init_logging() {
    # 创建日志目录
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
    fi

    # 创建主日志文件
    touch "$MAIN_LOG"
    log_info "主日志文件：$MAIN_LOG"

}

# 显示脚本头部信息
show_header() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "    时间：$(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================================"
}

# 检查命令是否存在
check_command() {
    local command="$1"
    local description="$2"

    if ! command -v "$command" &> /dev/null; then
        log_error "$description 未安装或未在PATH中：$command"
        return 1
    fi
    return 0
}

# 检查端口是否可用
check_port_available() {
    local port="$1"
    if lsof -ti:$port >/dev/null 2>&1; then
        return 1  # 端口被占用
    fi
    return 0  # 端口可用
}

# ==================== 环境检查函数 ====================

# 修复JDK结构
repair_jdk_structure() {
    local java_home="$1"
    
    if [ -z "$java_home" ] || [ ! -d "$java_home" ]; then
        return 0
    fi
    
    local required_files=("currency.data" "tzdb.dat")
    local links_to_create=()
    
    # 检查lib目录下是否缺少必需文件
    for file in "${required_files[@]}"; do
        local lib_file_path="$java_home/lib/$file"
        local jre_file_path="$java_home/jre/lib/$file"
        local relative_source_path="../jre/lib/$file"
        
        if [ ! -f "$lib_file_path" ] && [ -f "$jre_file_path" ]; then
            links_to_create+=("$file:$relative_source_path:$lib_file_path")
        fi
    done
    
    if [ ${#links_to_create[@]} -gt 0 ]; then
        log_info "检测到JDK结构不完整，需要修复以下文件："
        
        for link_info in "${links_to_create[@]}"; do
            IFS=':' read -r file_name source_path target_path <<< "$link_info"
            
            log_info "  缺少文件：$file_name"
            log_info "  源文件：$source_path"
            log_info "  目标位置：$target_path"
            
            # 确保目标目录存在
            local target_dir=$(dirname "$target_path")
            if [ ! -d "$target_dir" ]; then
                mkdir -p "$target_dir"
                log_info "创建目标目录：$target_dir"
            fi
            
            # 创建软链接
            if ln -sf "$source_path" "$target_path" 2>/dev/null; then
                log_success "成功创建软链接：$file_name"
                
                # 验证软链接
                if [ -L "$target_path" ] && [ -f "$target_path" ]; then
                    local source_size=$(stat -c%s "$source_path" 2>/dev/null || stat -f%z "$source_path" 2>/dev/null || echo "未知")
                    log_info "软链接验证通过：$file_name (源文件大小: $source_size 字节)"
                else
                    log_warning "软链接创建异常：$file_name"
                fi
            else
                log_error "创建软链接失败：$file_name"
            fi
        done
        
        log_success "JDK结构修复完成，共处理 ${#links_to_create[@]} 个文件"
    else
        # 检查文件是否存在
        local missing_files=()
        for file in "${required_files[@]}"; do
            local lib_file_path="$java_home/lib/$file"
            if [ ! -f "$lib_file_path" ]; then
                missing_files+=("$file")
            fi
        done
        
        if [ ${#missing_files[@]} -eq 0 ]; then
            log_info "JDK结构检查通过，所有必需文件都存在"
        else
            log_warning "JDK结构检查发现缺少文件：${missing_files[*]}"
            log_warning "这可能导致时区或货币相关功能异常"
        fi
    fi
}

# 设置Java环境
setup_java_environment() {
    local custom_java_home="$1"

    log_step "1" "设置Java环境"

    # 如果指定了自定义Java路径，则临时修改set-java-env.sh中的路径
    if [ -n "$custom_java_home" ]; then
        if [ -d "$custom_java_home" ] && [ -x "$custom_java_home/bin/java" ]; then
            # 临时设置环境变量，不修改脚本文件
            export JAVA_HOME="$custom_java_home"
            export PATH="$custom_java_home/bin:$PATH"
            export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
            export MAVEN_OPTS="$MAVEN_OPTS -Djava.security.policy=all.policy"
            export MAVEN_OPTS="$MAVEN_OPTS -Djava.home=$JAVA_HOME"
            
            # 检查并修复JDK结构问题
            repair_jdk_structure "$custom_java_home"
            
            log_info "使用指定的Java JDK：$custom_java_home"
        else
            log_error "指定的Java JDK路径无效：$custom_java_home"
            return 1
        fi
    else
        # 使用set-java-env.sh脚本设置环境变量
        local set_java_script="./scripts/set-java-env.sh"
        if [ -f "$set_java_script" ]; then
            log_info "调用Java环境设置脚本：$set_java_script"
            # 通过source执行脚本以保持环境变量
            if source "$set_java_script" >> "$MAIN_LOG" 2>&1; then
                log_info "Java环境设置脚本执行成功"
                
                # 检查并修复JDK结构问题
                if [ -n "$JAVA_HOME" ]; then
                    repair_jdk_structure "$JAVA_HOME"
                fi
            else
                log_warning "Java环境设置脚本执行失败，尝试使用系统默认Java"
            fi
        else
            log_warning "未找到Java环境设置脚本：$set_java_script"
            log_warning "使用系统默认Java环境"
        fi
    fi

    # 验证Java命令可用性
    if ! check_command "java" "Java运行时"; then
        return 1
    fi

    # 获取Java版本信息
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "当前Java版本：$java_version"

    # 验证Java版本兼容性
    if [[ "$java_version" =~ ^1\.8\. ]]; then
        log_success "使用Java 8，完全兼容"
    elif [[ "$java_version" =~ ^(11|17|21)\. ]]; then
        log_warning "使用Java $java_version，项目配置为Java 8，但应该向后兼容"
    else
        log_warning "当前Java版本：$java_version，可能存在兼容性问题"
    fi

    # 检查JDK完整性 - 特别是加密策略文件
    if [ -n "$JAVA_HOME" ]; then
        local security_dir="$JAVA_HOME/jre/lib/security"
        if [ ! -d "$security_dir" ]; then
            # 尝试其他可能的路径（Java 9+）
            security_dir="$JAVA_HOME/lib/security"
        fi
        
        if [ -d "$security_dir" ]; then
            # 检查关键的安全策略文件
            local policy_files=("java.policy" "java.security")
            local missing_files=()
            
            for file in "${policy_files[@]}"; do
                if [ ! -f "$security_dir/$file" ]; then
                    missing_files+=("$file")
                fi
            done
            
            if [ ${#missing_files[@]} -gt 0 ]; then
                log_warning "JDK安全目录缺少以下文件：${missing_files[*]}"
                log_warning "这可能导致SSL/TLS连接问题，建议重新安装JDK"
            else
                log_info "JDK安全策略文件检查通过"
            fi
        else
            log_warning "未找到JDK安全目录：$security_dir"
            log_warning "这可能导致加密功能异常，建议检查JDK安装"
        fi
    fi

    return 0
}

# 检查Maven环境
check_maven_environment() {
    log_step "2" "检查Maven环境"

    # 检查Maven命令
    if ! check_command "mvn" "Apache Maven"; then
        return 1
    fi

    # 获取Maven版本信息
    local maven_version=$(mvn -version | head -n 1)
    log_info "Maven版本：$maven_version"

    # 如果没有通过set-java-env.sh设置MAVEN_OPTS，则进行默认设置
    if [ -z "$MAVEN_OPTS" ]; then
        log_info "MAVEN_OPTS未设置，使用默认配置"
        export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
        
        if [ -n "$JAVA_HOME" ]; then
            # 检查并修复JDK结构问题
            repair_jdk_structure "$JAVA_HOME"
            
            export MAVEN_OPTS="$MAVEN_OPTS -Djava.home=$JAVA_HOME"
            log_info "Maven配置使用Java：$JAVA_HOME"
        fi
        
        # 检查javadoc命令是否可用
        if [ -n "$JAVA_HOME" ] && [ ! -x "$JAVA_HOME/bin/javadoc" ]; then
            log_warning "javadoc命令不可用，将跳过Javadoc生成"
            export MAVEN_OPTS="$MAVEN_OPTS -Dmaven.javadoc.skip=true"
        elif [ -z "$JAVA_HOME" ]; then
            log_warning "JAVA_HOME未设置，将跳过Javadoc生成"
            export MAVEN_OPTS="$MAVEN_OPTS -Dmaven.javadoc.skip=true"
        fi
    else
        log_info "使用已设置的MAVEN_OPTS"
    fi

    log_info "Maven选项：$MAVEN_OPTS"
    log_success "Maven环境检查完成"

    return 0
}

# 验证项目结构
validate_project_structure() {
    log_step "3" "验证项目结构"

    # 检查根目录pom.xml
    if [ ! -f "pom.xml" ]; then
        log_error "根目录pom.xml文件不存在"
        return 1
    fi
    log_info "根目录pom.xml文件存在"

    # 检查各个模块目录
    local missing_modules=()
    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            missing_modules+=("$module")
        else
            log_info "模块目录存在：$module"

            # 检查模块的pom.xml
            if [ ! -f "$module/pom.xml" ]; then
                log_warning "模块pom.xml不存在：$module/pom.xml"
            fi
        fi
    done

    # 检查独立服务模块（仅在build-test模式下需要）
    if [ ! -d "$STANDALONE_MODULE" ]; then
        log_warning "独立服务模块不存在：$STANDALONE_MODULE"
        log_warning "在build-test模式下将无法启动测试服务器"
    else
        log_info "独立服务模块存在：$STANDALONE_MODULE"
    fi

    # 报告缺失的模块
    if [ ${#missing_modules[@]} -gt 0 ]; then
        log_warning "以下模块目录不存在：${missing_modules[*]}"
        log_warning "将跳过这些模块的编译"
    fi

    log_success "项目结构验证完成"
    return 0
}

# 清理构建和测试环境
clean_environment() {
    log_step "4" "清理构建和测试环境"

    # 清理Maven构建缓存
    log_info "清理Maven构建缓存..."

    # 清理根目录target
    if [ -d "target" ]; then
        rm -rf target
        log_info "清理根目录target目录"
    fi

    # 清理各模块的target目录
    for module in "${PROJECT_MODULES[@]}"; do
        if [ -d "$module" ] && [ -d "$module/target" ]; then
            rm -rf "$module/target"
            log_info "清理模块target目录：$module"
        fi
    done

    # 清理独立服务模块的target目录
    if [ -d "$STANDALONE_MODULE" ] && [ -d "$STANDALONE_MODULE/target" ]; then
        rm -rf "$STANDALONE_MODULE/target"
        log_info "清理独立服务模块target目录：$STANDALONE_MODULE"
    fi

    # 清理测试数据目录
    local test_data_dirs=("./test-data" "./data" "./$STANDALONE_MODULE/data")
    for dir in "${test_data_dirs[@]}"; do
        if [ -d "$dir" ]; then
            rm -rf "$dir"
            log_info "清理测试数据目录：$dir"
        fi
    done

    # 清理临时文件
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "test-*.dat" -delete 2>/dev/null || true
    find . -name "*.test" -delete 2>/dev/null || true
    find . -name "*.log" -path "*/target/*" -delete 2>/dev/null || true

    # 停止可能运行的测试服务器进程
    stop_all_test_servers

    log_success "环境清理完成"
    return 0
}

# 停止所有测试服务器进程
stop_all_test_servers() {
    log_info "停止所有可能运行的测试服务器进程..."

    # 停止占用测试端口的进程
    local test_server_pids=$(lsof -ti:$TEST_SERVER_PORT 2>/dev/null || true)
    if [ -n "$test_server_pids" ]; then
        log_info "停止占用端口 $TEST_SERVER_PORT 的进程：$test_server_pids"
        kill -9 $test_server_pids 2>/dev/null || true
        sleep 2
    fi

    # 清理PID文件
    if [ -f "$SERVER_PID_FILE" ]; then
        rm -f "$SERVER_PID_FILE"
        log_info "清理服务器PID文件"
    fi
}

# ==================== 构建功能函数 ====================

# 编译项目
compile_project() {
    log_step "5" "编译项目"

    local start_time=$(date +%s)

    log_info "开始编译整个项目..."
    log_info "编译命令：mvn clean compile -T 1C"

    # 执行Maven编译，使用并行编译提高速度
    if timeout "$BUILD_TIMEOUT_SECONDS" mvn clean compile -T 1C \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        -Dmaven.compiler.encoding=UTF-8 \
        -Dproject.build.sourceEncoding=UTF-8 \
        >> "$MAIN_LOG" 2>&1; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_success "项目编译成功，耗时：${duration}秒"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_error "项目编译失败，耗时：${duration}秒"
        log_error "详细错误信息请查看日志文件：$MAIN_LOG"
        return 1
    fi
}

# 安装项目到本地仓库
install_project() {
    log_step "6" "安装项目到本地Maven仓库"

    local start_time=$(date +%s)

    log_info "开始安装项目到本地Maven仓库..."
    log_info "安装命令：mvn install -DskipTests -T 1C"

    # 执行Maven安装，跳过测试以提高速度
    if timeout "$BUILD_TIMEOUT_SECONDS" mvn install -DskipTests -T 1C \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        -Dmaven.compiler.encoding=UTF-8 \
        -Dproject.build.sourceEncoding=UTF-8 \
        >> "$MAIN_LOG" 2>&1; then

        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_success "项目安装成功，耗时：${duration}秒"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        log_error "项目安装失败，耗时：${duration}秒"
        log_error "详细错误信息请查看日志文件：$MAIN_LOG"
        return 1
    fi
}

# 验证构建结果
verify_build_results() {
    log_step "7" "验证构建结果"

    local success_count=0
    local total_count=0

    # 检查各模块的编译结果
    for module in "${PROJECT_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            continue
        fi

        total_count=$((total_count + 1))

        # 检查target/classes目录是否存在
        if [ -d "$module/target/classes" ]; then
            local class_count=$(find "$module/target/classes" -name "*.class" | wc -l)
            if [ "$class_count" -gt 0 ]; then
                log_info "模块编译成功：$module (生成 $class_count 个class文件)"
                success_count=$((success_count + 1))
            else
                log_warning "模块编译异常：$module (未生成class文件)"
            fi
        else
            log_warning "模块编译失败：$module (target/classes目录不存在)"
        fi

        # 检查JAR文件是否生成
        if [ -d "$module/target" ]; then
            local jar_files=$(find "$module/target" -name "*.jar" | wc -l)
            if [ "$jar_files" -gt 0 ]; then
                log_info "模块JAR文件生成：$module ($jar_files 个JAR文件)"
            fi
        fi
    done

    # 输出验证结果
    log_info "编译验证结果：$success_count/$total_count 个模块编译成功"

    if [ "$success_count" -eq "$total_count" ]; then
        log_success "所有模块编译验证通过"
        return 0
    else
        log_warning "部分模块编译验证失败"
        return 1
    fi
}

# ==================== 测试功能函数 ====================

# 运行单元测试
run_unit_tests() {
    log_step "8" "运行单元测试"
    log_test_phase "开始单元测试阶段"

    local total_modules=0
    local success_modules=0
    local start_time=$(date +%s)

    # 只对需要测试的模块执行单元测试
    for module in "${TEST_MODULES[@]}"; do
        if [ ! -d "$module" ]; then
            log_warning "模块目录不存在，跳过：$module"
            continue
        fi

        total_modules=$((total_modules + 1))

        log_test_phase "运行模块单元测试：$module"

        # 运行单元测试，排除集成测试
        if timeout "$TEST_TIMEOUT_SECONDS" mvn test -pl "$module" \
            -Dmaven.compiler.source=1.8 \
            -Dmaven.compiler.target=1.8 \
            -Dtest='!**/*IntegrationTest,!**/*EndToEndTest' \
            >> "$MAIN_LOG" 2>&1; then

            log_success "模块单元测试通过：$module"
            success_modules=$((success_modules + 1))
        else
            log_error "模块单元测试失败：$module"
        fi
    done

    # 跳过演示模块的单元测试，记录说明信息
    for demo_module in "${DEMO_MODULES[@]}"; do
        if [ -d "$demo_module" ]; then
            log_info "跳过演示模块单元测试：$demo_module（演示模块将在集成测试阶段执行）"
        fi
    done

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_test_phase "单元测试阶段完成"
    log_info "单元测试结果：$success_modules/$total_modules 个模块通过，耗时：${duration}秒"

    if [ "$success_modules" -eq "$total_modules" ]; then
        log_success "所有单元测试通过"
        return 0
    else
        log_error "部分单元测试失败"
        return 1
    fi
}

# ==================== 服务器管理函数 ====================

# 启动测试服务器
start_test_server() {
    log_test_phase "启动测试服务器"
    log_info "启动测试服务器（端口：$TEST_SERVER_PORT）..."

    # 检查独立服务器模块是否存在
    if [ ! -d "$STANDALONE_MODULE" ]; then
        log_error "独立服务器模块不存在，无法启动测试服务器：$STANDALONE_MODULE"
        return 1
    fi

    # 检查启动脚本是否存在
    if [ ! -f "$STANDALONE_MODULE/start-server.sh" ]; then
        log_error "独立服务器启动脚本不存在，无法启动测试服务器"
        return 1
    fi

    # 确保端口未被占用
    if ! check_port_available "$TEST_SERVER_PORT"; then
        log_error "端口 $TEST_SERVER_PORT 已被占用"
        return 1
    fi

    # 首先编译独立服务器模块（如果还没有编译）
    log_info "确保独立服务器模块已编译..."
    if [ ! -f "$STANDALONE_MODULE/target/file-transfer-server-standalone-1.0.0.jar" ]; then
        log_info "编译独立服务器模块..."
        if ! mvn package -pl "$STANDALONE_MODULE" -DskipTests -q >> "$MAIN_LOG" 2>&1; then
            log_error "独立服务器模块编译失败"
            return 1
        fi
    fi

    # 启动独立服务器作为测试服务器
    log_info "使用独立服务器启动脚本启动测试服务器..."
    cd "$STANDALONE_MODULE"

    # 使用启动脚本启动服务器
    if ./start-server.sh start --port "$TEST_SERVER_PORT" --background >> "$MAIN_LOG" 2>&1; then
        # 获取服务器PID
        local server_pid
        if [ -f "logs/server.pid" ]; then
            server_pid=$(cat logs/server.pid)
            echo $server_pid > "../$SERVER_PID_FILE"

            cd ..
            log_info "测试服务器启动中（PID：$server_pid）..."

            # 等待服务器启动
            local wait_count=0
            while [ $wait_count -lt $SERVER_STARTUP_TIMEOUT_SECONDS ]; do
                # 使用独立服务器的健康检查端点（注意context-path前缀）
                if curl -s "http://localhost:$TEST_SERVER_PORT/filetransfer/actuator/health" >/dev/null 2>&1; then
                    log_success "测试服务器启动成功"
                    log_info "服务器地址: http://localhost:$TEST_SERVER_PORT"
                    log_info "API文档: http://localhost:$TEST_SERVER_PORT/filetransfer/doc.html"
                    log_info "健康检查: http://localhost:$TEST_SERVER_PORT/filetransfer/actuator/health"
                    return 0
                fi

                sleep 1
                wait_count=$((wait_count + 1))

                # 检查进程是否还在运行
                if ! kill -0 $server_pid 2>/dev/null; then
                    log_error "测试服务器进程意外退出"
                    cd "$STANDALONE_MODULE"
                    ./start-server.sh stop >> "$MAIN_LOG" 2>&1 || true
                    cd ..
                    return 1
                fi
            done

            log_error "测试服务器启动超时（${SERVER_STARTUP_TIMEOUT_SECONDS}秒）"
            cd "$STANDALONE_MODULE"
            ./start-server.sh stop >> "$MAIN_LOG" 2>&1 || true
            cd ..
            return 1
        else
            cd ..
            log_error "无法获取测试服务器PID"
            return 1
        fi
    else
        cd ..
        log_error "独立服务器启动失败"
        return 1
    fi
}

# 停止测试服务器
stop_test_server() {
    log_test_phase "停止测试服务器"
    log_info "停止测试服务器..."

    # 首先尝试使用独立服务器的停止脚本
    if [ -d "$STANDALONE_MODULE" ] && [ -f "$STANDALONE_MODULE/start-server.sh" ]; then
        log_info "使用独立服务器启动脚本停止测试服务器..."
        cd "$STANDALONE_MODULE"
        ./start-server.sh stop >> "$MAIN_LOG" 2>&1 || true
        cd ..
    fi

    # 如果PID文件存在，也尝试通过PID停止
    if [ -f "$SERVER_PID_FILE" ]; then
        local server_pid=$(cat "$SERVER_PID_FILE")
        if kill -0 $server_pid 2>/dev/null; then
            log_info "通过PID停止测试服务器（PID：$server_pid）..."
            kill $server_pid 2>/dev/null || true

            # 等待进程正常退出
            local wait_count=0
            while [ $wait_count -lt $SERVER_SHUTDOWN_TIMEOUT_SECONDS ]; do
                if ! kill -0 $server_pid 2>/dev/null; then
                    break
                fi
                sleep 1
                wait_count=$((wait_count + 1))
            done

            # 强制杀死如果还在运行
            if kill -0 $server_pid 2>/dev/null; then
                log_warning "强制停止测试服务器进程（PID：$server_pid）"
                kill -9 $server_pid 2>/dev/null || true
            fi

            log_info "测试服务器已停止（PID：$server_pid）"
        fi
        rm -f "$SERVER_PID_FILE"
    fi

    # 确保端口释放
    local remaining_pids=$(lsof -ti:$TEST_SERVER_PORT 2>/dev/null || true)
    if [ -n "$remaining_pids" ]; then
        log_warning "强制停止占用端口 $TEST_SERVER_PORT 的进程：$remaining_pids"
        kill -9 $remaining_pids 2>/dev/null || true
        sleep 2
    fi

    log_success "测试服务器停止完成"
}

# 运行客户端演示测试
run_client_demo_test() {
    log_test_phase "运行客户端演示测试"

    # 检查客户端演示模块是否存在
    if [ ! -d "file-transfer-client-demo" ]; then
        log_warning "客户端演示模块不存在，跳过演示测试"
        return 1
    fi

    local demo_start_time=$(date +%s)
    local demo_success=true

    # 设置演示配置
    local demo_server_host="localhost"
    local demo_server_port="$TEST_SERVER_PORT"

    log_info "启动客户端演示测试（服务器：$demo_server_host:$demo_server_port）"

    # 运行客户端演示，确保使用与服务端配置一致的用户认证信息
    if timeout "$DEMO_TEST_TIMEOUT_SECONDS" mvn exec:java -pl file-transfer-client-demo \
        -Dexec.mainClass="com.sdesrd.filetransfer.demo.FileTransferClientDemo" \
        -Ddemo.server.host="$demo_server_host" \
        -Ddemo.server.port="$demo_server_port" \
        -Ddemo.user.name="demo" \
        -Ddemo.user.secret="demo-secret-key-2024" \
        -Ddemo.upload.dir="demo-files/upload" \
        -Ddemo.download.dir="demo-files/download" \
        -Dmaven.compiler.source=1.8 \
        -Dmaven.compiler.target=1.8 \
        >> "$MAIN_LOG" 2>&1; then

        log_success "客户端演示测试执行成功"
    else
        log_warning "客户端演示测试执行失败或超时（${DEMO_TEST_TIMEOUT_SECONDS}秒）"
        demo_success=false
    fi

    local demo_end_time=$(date +%s)
    local demo_duration=$((demo_end_time - demo_start_time))

    log_info "客户端演示测试耗时：${demo_duration}秒"

    # 清理演示文件
    if [ -d "demo-files" ]; then
        rm -rf demo-files 2>/dev/null || true
        log_info "清理演示文件目录"
    fi

    if [ "$demo_success" = true ]; then
        return 0
    else
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    log_step "9" "运行集成测试"
    log_test_phase "开始集成测试阶段"

    local start_time=$(date +%s)
    local integration_success=true

    # 启动测试服务器
    if ! start_test_server; then
        log_error "无法启动测试服务器，跳过集成测试"
        return 1
    fi

    # 确保服务器启动后有足够时间稳定
    log_info "等待测试服务器稳定..."
    sleep 3

    # 检查是否有其他模块包含端到端测试
    log_test_phase "检查并运行端到端测试"
    local end_to_end_test_found=false
    for module in "${PROJECT_MODULES[@]}"; do
        if [ -d "$module/src/test/java" ]; then
            if find "$module/src/test/java" -name "*EndToEndTest.java" -o -name "*EndToEndTransferTest.java" -o -name "*IntegrationTest.java" | grep -q .; then
                log_test_phase "在模块 $module 中发现端到端/集成测试，正在执行..."
                # 运行集成测试时传递正确的用户认证配置，确保与服务端配置一致
                if timeout "$TEST_TIMEOUT_SECONDS" mvn test -pl "$module" \
                    -Dtest="*EndToEndTest,*EndToEndTransferTest,*IntegrationTest" \
                    -Dserver.port=$TEST_SERVER_PORT \
                    -Dtest.server.host=localhost \
                    -Dtest.server.port=$TEST_SERVER_PORT \
                    -Dtest.user.name=demo \
                    -Dtest.user.secret=demo-secret-key-2024 \
                    -Dmaven.compiler.source=1.8 \
                    -Dmaven.compiler.target=1.8 \
                    >> "$MAIN_LOG" 2>&1; then
                    log_success "模块 $module 端到端/集成测试通过"
                    end_to_end_test_found=true
                else
                    log_error "模块 $module 端到端/集成测试失败"
                    integration_success=false
                fi
            fi
        fi
    done

    # 运行客户端演示测试
    if run_client_demo_test; then
        log_success "客户端演示测试通过"
    else
        log_warning "客户端演示测试失败，但不影响整体测试结果"
    fi

    # 停止测试服务器
    stop_test_server

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_test_phase "集成测试阶段完成"
    log_info "集成测试耗时：${duration}秒"

    if [ "$integration_success" = true ]; then
        log_success "集成测试通过"
        return 0
    else
        log_error "集成测试失败"
        return 1
    fi
}

# ==================== 报告生成函数 ====================

# 收集测试结果
collect_test_results() {
    local total_tests=0
    local failed_tests=0
    local skipped_tests=0

    # 收集各模块的测试结果（只统计有单元测试的模块）
    local unique_modules=("${TEST_MODULES[@]}")

    for module in "${unique_modules[@]}"; do
        if [ ! -d "$module/target/surefire-reports" ]; then
            continue
        fi

        # 统计测试文件数量
        local module_test_files=$(find "$module/target/surefire-reports" -name "TEST-*.xml" | wc -l)

        if [ "$module_test_files" -gt 0 ]; then
            # 解析测试结果XML文件
            local module_tests=0
            local module_failures=0
            local module_errors=0
            local module_skipped=0

            for xml_file in "$module/target/surefire-reports/TEST-"*.xml; do
                if [ -f "$xml_file" ]; then
                    # 使用grep和awk解析XML属性
                    local test_info=$(grep '<testsuite' "$xml_file" | head -n 1)
                    if [ -n "$test_info" ]; then
                        local tests=$(echo "$test_info" | sed -n 's/.*tests="\([^"]*\)".*/\1/p')
                        local failures=$(echo "$test_info" | sed -n 's/.*failures="\([^"]*\)".*/\1/p')
                        local errors=$(echo "$test_info" | sed -n 's/.*errors="\([^"]*\)".*/\1/p')
                        local skipped=$(echo "$test_info" | sed -n 's/.*skipped="\([^"]*\)".*/\1/p')

                        module_tests=$((module_tests + ${tests:-0}))
                        module_failures=$((module_failures + ${failures:-0}))
                        module_errors=$((module_errors + ${errors:-0}))
                        module_skipped=$((module_skipped + ${skipped:-0}))
                    fi
                fi
            done

            local module_failed=$((module_failures + module_errors))

            total_tests=$((total_tests + module_tests))
            failed_tests=$((failed_tests + module_failed))
            skipped_tests=$((skipped_tests + module_skipped))

            log_info "$module: $module_tests 个测试，$module_failed 个失败，$module_skipped 个跳过"
        else
            log_warning "$module: 未找到测试结果文件"
        fi
    done

    local passed_tests=$((total_tests - failed_tests - skipped_tests))

    # 输出测试结果汇总
    echo ""
    echo "=========================================="
    echo "           测试结果汇总"
    echo "=========================================="
    echo "总测试数: $total_tests"
    echo "通过测试: $passed_tests"
    echo "失败测试: $failed_tests"
    echo "跳过测试: $skipped_tests"

    if [ "$failed_tests" -eq 0 ]; then
        echo -e "测试结果: ${COLOR_GREEN}全部通过${COLOR_NC}"
        return 0
    else
        echo -e "测试结果: ${COLOR_RED}有失败${COLOR_NC}"
        return 1
    fi
}

# 生成最终报告
generate_final_report() {
    local execution_mode="$1"
    log_step "10" "生成最终报告"

    local report_file="$LOG_DIR/final-report-$(date +%Y%m%d_%H%M%S).txt"

    {
        echo "========================================================"
        echo "        文件传输SDK构建和测试最终报告"
        echo "========================================================"
        echo "执行时间：$(date '+%Y-%m-%d %H:%M:%S')"
        echo "脚本版本：$SCRIPT_VERSION"
        echo "Java版本：$(java -version 2>&1 | head -n 1)"
        echo "Maven版本：$(mvn -version | head -n 1)"
        echo ""

        echo "执行模式："
        if [ "$execution_mode" = "$MODE_BUILD" ]; then
            echo "  构建模式：仅编译项目"
        else
            echo "  完整模式：构建 + 测试"
        fi
        echo ""

        echo "项目模块："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ -d "$module" ]; then
                echo "  ✓ $module"
            else
                echo "  ✗ $module (目录不存在)"
            fi
        done
        echo ""

        echo "构建结果："
        for module in "${PROJECT_MODULES[@]}"; do
            if [ ! -d "$module" ]; then
                continue
            fi

            if [ -d "$module/target/classes" ]; then
                local class_count=$(find "$module/target/classes" -name "*.class" | wc -l)
                echo "  $module: 编译成功 ($class_count 个class文件)"
            else
                echo "  $module: 编译失败"
            fi
        done
        echo ""

        if [ "$execution_mode" = "$MODE_BUILD_TEST" ]; then
            echo "测试结果详情："
            # 统计有单元测试的模块
            for module in "${TEST_MODULES[@]}"; do
                if [ ! -d "$module/target/surefire-reports" ]; then
                    echo "  $module: 未运行测试"
                    continue
                fi

                local test_files=$(find "$module/target/surefire-reports" -name "TEST-*.xml" | wc -l)
                if [ "$test_files" -gt 0 ]; then
                    echo "  $module: $test_files 个测试套件"
                else
                    echo "  $module: 无测试结果"
                fi
            done

            # 说明演示模块的测试方式
            for demo_module in "${DEMO_MODULES[@]}"; do
                if [ -d "$demo_module" ]; then
                    echo "  $demo_module: 演示模块（通过集成测试中的演示程序执行验证）"
                fi
            done
            echo ""

        fi

        echo "详细日志：$MAIN_LOG"
        echo "========================================================"

    } > "$report_file"

    log_info "最终报告已生成：$report_file"

    # 显示报告内容
    cat "$report_file"

    return 0
}

# ==================== 帮助信息 ====================

# 显示帮助信息
show_help() {
    echo "========================================================"
    echo "    $SCRIPT_NAME"
    echo "    版本：$SCRIPT_VERSION"
    echo "========================================================"
    echo ""
    echo "用法: $0 [模式] [选项]"
    echo ""
    echo "执行模式："
    echo "  build                 仅执行构建（编译+安装），不运行测试"
    echo "  build-test            执行完整流程（构建+测试）[默认]"
    echo ""
    echo "Java环境选项："
    echo "  --java-home PATH      指定Java JDK路径"
    echo ""
    echo "其他选项："
    echo "  --help                显示此帮助信息"
    echo ""
    echo "使用示例："
    echo "  $0                              # 完整构建和测试流程"
    echo "  $0 build                        # 仅构建项目"
    echo "  $0 build-test                   # 构建并测试项目"
    echo "  $0 --java-home /path/to/java    # 使用指定的Java路径"
    echo ""
    echo "默认配置："
    echo "  Java路径: 由scripts/set-java-env.sh脚本管理"
    echo "  测试端口: $TEST_SERVER_PORT"
    echo "  日志目录: $LOG_DIR"
    echo ""
    echo "说明："
    echo "  - build模式：编译项目并安装到本地Maven仓库"
    echo "  - build-test模式：在build基础上运行单元测试和集成测试"
    echo "  - 集成测试会自动启动和停止file-transfer-standalone服务"
    echo "  - 所有日志都会记录到日志文件中，便于问题排查"
    echo ""
}

# ==================== 主程序 ====================

# 清理函数（脚本退出时调用）
cleanup_on_exit() {
    local exit_code=$?

    # 停止测试服务器
    stop_test_server

    if [ $exit_code -ne 0 ]; then
        log_error "执行过程中发生错误，退出码：$exit_code"
        log_info "详细错误信息请查看日志文件：$MAIN_LOG"
    fi

    # 恢复原始环境变量
    if [ -n "$ORIGINAL_JAVA_HOME" ]; then
        export JAVA_HOME="$ORIGINAL_JAVA_HOME"
    fi

    if [ -n "$ORIGINAL_PATH" ]; then
        export PATH="$ORIGINAL_PATH"
    fi
}

# 主函数
main() {
    # 保存原始环境变量
    export ORIGINAL_JAVA_HOME="$JAVA_HOME"
    export ORIGINAL_PATH="$PATH"

    # 设置退出时清理
    trap cleanup_on_exit EXIT

    # 解析命令行参数
    local custom_java_home=""
    local execution_mode="$MODE_BUILD_TEST"  # 默认为构建+测试模式

    while [[ $# -gt 0 ]]; do
        case $1 in
            build)
                execution_mode="$MODE_BUILD"
                shift
                ;;
            build-test)
                execution_mode="$MODE_BUILD_TEST"
                shift
                ;;
            --java-home)
                custom_java_home="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                echo ""
                show_help
                exit 1
                ;;
        esac
    done

    # 显示脚本头部信息
    show_header

    # 初始化日志
    init_logging

    log_info "执行模式：$execution_mode"

    # 执行主要流程
    local execution_failed=false

    # 步骤1-4：环境检查和准备
    if ! setup_java_environment "$custom_java_home"; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && ! check_maven_environment; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && ! validate_project_structure; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && ! clean_environment; then
        execution_failed=true
    # 步骤5-7：构建流程
    elif [ "$execution_failed" = false ] && ! compile_project; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && ! install_project; then
        execution_failed=true
    elif [ "$execution_failed" = false ] && ! verify_build_results; then
        execution_failed=true
    fi

    # 步骤8-11：测试流程（仅在build-test模式下执行）
    if [ "$execution_failed" = false ] && [ "$execution_mode" = "$MODE_BUILD_TEST" ]; then
        log_test_phase "开始测试流程"

        if ! run_unit_tests; then
            execution_failed=true
        elif ! run_integration_tests; then
            execution_failed=true
        fi

        # 收集测试结果
        if ! collect_test_results; then
            execution_failed=true
        fi
    fi

    # 生成最终报告
    generate_final_report "$execution_mode"

    # 返回结果
    if [ "$execution_failed" = true ]; then
        log_error "执行失败"
        exit 1
    else
        if [ "$execution_mode" = "$MODE_BUILD" ]; then
            log_success "构建成功完成"
        else
            log_success "构建和测试成功完成"
        fi
        exit 0
    fi
}

# 执行主函数
main "$@" 
