# File Transfer Server SDK 整合指南

## 📖 概述

本指南详细说明如何将File Transfer Server SDK整合到已有的Spring Boot单体项目中，特别针对使用JeecG框架、配置了Shiro鉴权、设置了Context Path的项目提供详细的整合方案。

File Transfer SDK是一个基于Spring Boot的、生产级的高性能文件传输SDK，提供分块上传、断点续传、智能秒传、多用户管理、权限控制、动态限速、数据库容错、智能清理机制等一系列强大功能。

## 🚀 快速整合步骤

### 1. 添加依赖

在你现有的Spring Boot项目的`pom.xml`中添加server端SDK依赖：

```xml
<dependency>
    <groupId>com.sdesrd.filetransfer</groupId>
    <artifactId>file-transfer-server-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 配置文件整合

#### 针对原项目的完整 application.yml 配置：

```yaml
server:
  port: 49010
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /training  # ⚠️ 这会影响文件传输API路径
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

spring:
  servlet:
    multipart:
      # ⚠️ 注意：SDK会复用这些配置
      max-file-size: 300MB      # 保持你的原配置
      max-request-size: 300MB   # 保持你的原配置
  
  # ... 你的其他spring配置保持不变 ...
  
  autoconfigure:
    exclude: 
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
      # 如果不想让SDK使用SQLite，可以排除SDK的数据库自动配置
      # - com.sdesrd.filetransfer.server.config.DatabaseInitializer

# 🎯 文件传输SDK配置 - 新增部分
file:
  transfer:
    server:
      enabled: true  # 启用文件传输功能
      
      # 数据库配置 - 默认使用SQLite，与你的MySQL分离
      database-path: /home/<USER>/trainsaas/data/file-transfer/database.db
      
      # 功能开关
      swagger-enabled: true    # API文档
      cors-enabled: true       # 跨域支持
      allowed-origins: 
        - "*"                  # 允许的跨域来源
      
      # 智能清理配置
      cleanup-enabled: true            # 是否启用自动清理功能
      cleanup-interval: 3600000        # 清理间隔时间（毫秒），默认1小时
      record-expire-time: 86400000     # 传输记录过期时间（毫秒），默认24小时
      chunk-expire-time: 604800000     # 分块记录过期时间（毫秒），默认7天
      failed-record-retain-time: 259200000  # 失败记录保留时间（毫秒），默认3天
      max-batch-delete-size: 1000      # 批量删除的最大记录数，避免数据库性能问题
      
      # 用户配置（多用户支持）
      users:
        # "admin" 用户拥有管理员权限
        admin:
          role: "admin"                 # 管理员角色，可访问管理接口
          secret-key: "your-admin-secret-key-2024"
          storage-path: ./data/file-transfer/admin/files
          upload-rate-limit: 52428800   # 上传限速: 50 MB/s (字节/秒)
          download-rate-limit: 52428800 # 下载限速: 50 MB/s
          max-file-size: 1073741824     # 最大文件: 1 GB
          default-chunk-size: 4194304   # 分块大小: 4 MB
          max-in-memory-size: 52428800  # 最大内存: 50 MB
          fast-upload-enabled: true     # 启用秒传
          rate-limit-enabled: false     # 管理员不限速

        # "demo" 用户用于演示和测试
        demo:
          role: "user"                  # 普通用户角色
          secret-key: "demo-secret-key-2024"
          storage-path: ./data/demo/files
          upload-rate-limit: 10485760   # 上传限速: 10 MB/s
          download-rate-limit: 10485760 # 下载限速: 10 MB/s
          max-file-size: 104857600      # 最大文件: 100 MB
          default-chunk-size: 1048576   # 分块大小: 1 MB
          max-in-memory-size: 10485760  # 最大内存: 10 MB
          fast-upload-enabled: true     # 启用秒传
          rate-limit-enabled: true      # 启用速度限制
        

# 🔐 Shiro配置修改 - 关键部分
jeecg:
  # ... 你的其他配置保持不变 ...
  
  shiro:
    # ⚠️ 重要：需要将文件传输API添加到排除列表
    excludeUrls: /sys/**,/jmreport/**,/login/**,/cas/login,/jeecg/**,/auth/**,/appdev/**,/docs/**,/ver_*,/**/category/getRandomBackground,/api/file/**
    # 是否启用超级管理员模式，开启后可跳过接口鉴权
    adminSuperMode: false

# ... 其他配置保持不变 ...

#swagger - 如果需要合并文档
knife4j:
  enable: true
  production: false
  basic:
    enable: true
    username: jeecg
    password: jeecg1314
```

#### application.properties 配置示例：

```properties
# 你的现有服务端口和context-path
server.port=49010
server.servlet.context-path=/training

# 文件传输SDK基础配置
file.transfer.server.enabled=true
file.transfer.server.database-path=./data/file-transfer/database.db
file.transfer.server.swagger-enabled=true
file.transfer.server.cors-enabled=true
file.transfer.server.allowed-origins[0]=*

# 用户配置
file.transfer.server.users.admin.secret-key=training-admin-secret-2024
file.transfer.server.users.admin.storage-path=./data/file-transfer/admin
file.transfer.server.users.admin.upload-rate-limit=52428800
file.transfer.server.users.admin.max-file-size=524288000

# Shiro排除路径 - 重要！
# 注意：由于统一使用context-path配置，这里需要包含完整路径
jeecg.shiro.excludeUrls=/sys/**,/jmreport/**,/login/**,/cas/login,/jeecg/**,/auth/**,/appdev/**,/docs/**,/ver_*,/**/category/getRandomBackground,/api/file/**
```

### 3. 启动类配置（可选）

如果遇到包扫描问题，可以在你的启动类中添加：

```java
@SpringBootApplication
@ComponentScan(basePackages = {
    "org.jeecg",                    // 你的JeecG包
    "com.sdesrd.filetransfer.server"       // SDK包
})
public class TrainingApplication {
    public static void main(String[] args) {
        SpringApplication.run(TrainingApplication.class, args);
    }
}
```

> **注意**: SDK使用了`@EnableAutoConfiguration`，通常不需要手动添加包扫描。

## 🔧 端口配置说明

### ❓ 问题：SDK会占用额外端口吗？

**答案：不会！** SDK设计为**库模式**，会复用你现有项目的端口。

**注意**：项目还提供了 `file-transfer-server-standalone` 模块，这是一个独立的服务端应用，默认使用端口49011，可以作为独立进程运行。

### 📍 API端点映射

由于你的项目设置了 `context-path: /training`，文件传输API路径会变为：

#### 文件传输接口 (`/api/file`)
```
http://localhost:49010/training/api/file/upload/init       # 初始化上传
http://localhost:49010/training/api/file/upload/chunk      # 分块上传
http://localhost:49010/training/api/file/upload/complete/{transferId}  # 完成上传
http://localhost:49010/training/api/file/download/{fileId} # 文件下载
http://localhost:49010/training/api/file/download/info/{fileId} # 获取文件信息
http://localhost:49010/training/api/file/download/chunk/{fileId} # 分块下载
http://localhost:49010/training/api/file/progress/{transferId} # 查询进度
http://localhost:49010/training/api/file/health            # 健康检查
```

#### 管理接口 (`/api/admin`) - 需要管理员权限
```
http://localhost:49010/training/api/admin/statistics       # 获取传输统计信息
http://localhost:49010/training/api/admin/health           # 获取系统健康状况
http://localhost:49010/training/api/admin/clear-rate-limiters # 清理限流器缓存
http://localhost:49010/training/api/admin/cleanup/statistics # 获取清理统计信息
http://localhost:49010/training/api/admin/cleanup/manual   # 手动触发清理操作
http://localhost:49010/training/api/admin/cleanup/config   # 获取清理配置信息
```

#### 数据库管理接口 (`/api/database`) - 需要管理员权限
```
http://localhost:49010/training/api/database/health        # 检查数据库健康状态
http://localhost:49010/training/api/database/backup        # 创建数据库备份
http://localhost:49010/training/api/database/backups       # 列出数据库备份
http://localhost:49010/training/api/database/backup/download/{fileName} # 下载备份文件
http://localhost:49010/training/api/database/rebuild       # 重建数据库
```

### 📚 API文档访问

```
http://localhost:49010/training/doc.html                   # Knife4j API文档
http://localhost:49010/training/swagger-ui.html            # Swagger UI（如果启用）
```

### 🔐 认证机制说明

SDK使用基于 `HMAC-SHA256` 的无状态认证机制：

#### 认证头格式
需要认证的接口必须在HTTP请求头中包含以下两个字段：
- `X-File-Transfer-User`: 用户名 (例如: `admin`)
- `X-File-Transfer-Auth`: 认证令牌

#### 角色权限控制
- **普通用户** (`role: "user"`): 只能访问基础文件传输接口 (`/api/file/*`)
- **管理员** (`role: "admin"`): 可以访问所有接口，包括管理接口 (`/api/admin/*`) 和数据库管理接口 (`/api/database/*`)

## ⚠️ 重要注意点

### 1. **Shiro鉴权处理**

**问题**: Shiro会对文件传输接口起效，导致认证失败。
**解决**: 必须在 `excludeUrls` 中添加 `/api/file/**`

#### 方案A：完全排除（推荐）
```yaml
jeecg:
  shiro:
    excludeUrls: /sys/**,/jmreport/**,/login/**,/cas/login,/jeecg/**,/auth/**,/appdev/**,/docs/**,/ver_*,/**/category/getRandomBackground,/api/file/**
```

**注意**: 如果需要管理员权限的接口，还需要排除管理接口：
```yaml
jeecg:
  shiro:
    excludeUrls: /sys/**,/jmreport/**,/login/**,/cas/login,/jeecg/**,/auth/**,/appdev/**,/docs/**,/ver_*,/**/category/getRandomBackground,/api/file/**,/api/admin/**,/api/database/**
```

#### 方案B：自定义鉴权逻辑
```java
@Configuration
public class FileTransferShiroConfig {
    
    @Bean
    public ShiroFilterFactoryBean shiroFilter() {
        ShiroFilterFactoryBean factoryBean = new ShiroFilterFactoryBean();
        
        Map<String, String> filterMap = new LinkedHashMap<>();
        // 文件传输API使用自定义认证
        filterMap.put("/api/file/**", "fileTransferAuth");
        
        factoryBean.setFilterChainDefinitionMap(filterMap);
        return factoryBean;
    }
    
    @Bean("fileTransferAuth")
    public FileTransferAuthFilter fileTransferAuthFilter() {
        return new FileTransferAuthFilter();
    }
}
```

### 2. **文件上传配置协调**

你的原配置会被SDK复用：
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 300MB      # SDK会使用这个配置
      max-request-size: 300MB   # SDK会使用这个配置
```

建议SDK配置保持一致：
```yaml
file:
  transfer:
    server:
      default:
        max-file-size: 314572800  # 300MB，与Spring配置一致
```

### 3. **数据存储策略**

#### 选项A：独立存储（推荐）
```yaml
file:
  transfer:
    server:
      database-path: /home/<USER>/trainsaas/data/file-transfer/database.db  # 独立SQLite
      default:
        storage-path: /home/<USER>/trainsaas/data/file-transfer/files        # 独立文件存储
```

#### 选项B：共享MySQL数据库
```java
@Configuration
public class FileTransferDataSourceConfig {
    
    @Autowired
    @Qualifier("master")
    private DataSource masterDataSource;
    
    @Bean
    @Primary
    public DataSource fileTransferDataSource() {
        // 使用你现有的master数据源
        return masterDataSource;
    }
}
```

**注意**：如果使用MySQL，需要在你的 `training_promotion` 数据库中执行以下SQL：

```sql
-- 文件传输记录表
CREATE TABLE IF NOT EXISTS file_transfer_record (
    id VARCHAR(64) PRIMARY KEY,
    file_id VARCHAR(128) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path VARCHAR(500),
    transferred_size BIGINT NOT NULL DEFAULT 0,
    total_chunks INT NOT NULL DEFAULT 0,
    completed_chunks INT NOT NULL DEFAULT 0,
    status INT NOT NULL DEFAULT 0,
    client_ip VARCHAR(45),
    create_time DATETIME NOT NULL,
    update_time DATETIME NOT NULL,
    complete_time DATETIME,
    fail_reason TEXT,
    file_type VARCHAR(50),
    ext_info TEXT,
    INDEX idx_file_id (file_id),
    INDEX idx_status (status),
    INDEX idx_client_ip (client_ip),
    INDEX idx_create_time (create_time)
);

-- 文件分块记录表
CREATE TABLE IF NOT EXISTS file_chunk_record (
    id VARCHAR(64) PRIMARY KEY,
    transfer_id VARCHAR(64) NOT NULL,
    file_id VARCHAR(128) NOT NULL,
    chunk_index INT NOT NULL,
    chunk_size BIGINT,
    chunk_offset BIGINT NOT NULL,
    chunk_path VARCHAR(500),
    chunk_md5 VARCHAR(32),
    status INT NOT NULL DEFAULT 0,
    retry_count INT NOT NULL DEFAULT 0,
    create_time DATETIME NOT NULL,
    complete_time DATETIME,
    fail_reason TEXT,
    UNIQUE KEY uk_transfer_chunk (transfer_id, chunk_index),
    INDEX idx_transfer_id (transfer_id),
    INDEX idx_file_id_chunk (file_id),
    INDEX idx_status_chunk (status),
    INDEX idx_chunk_index (chunk_index)
);
```

## 🗂️ 目录结构

整合后，项目目录结构如下：

```
training-project/
├── src/main/java/org/jeecg/
│   ├── TrainingApplication.java      # 你的启动类
│   ├── modules/                      # 你的业务模块
│   └── ...                          # 你的其他代码
├── src/main/resources/
│   ├── application.yml               # 你的配置文件
│   └── ...
└── /home/<USER>/trainsaas/             # 运行时文件
    ├── data/
    │   └── file-transfer/
    │       ├── database.db           # SQLite数据库
    │       ├── files/                # 默认文件存储
    │       ├── admin/                # admin用户文件
    │       ├── teacher/              # teacher用户文件
    │       └── student/              # student用户文件
    ├── upload-images/               # 你原有的上传目录
    └── document/                    # 你原有的文档目录
```

## 🔐 认证配置

### 多用户认证

SDK支持多用户，每个用户有独立的：
- 存储路径
- 速度限制
- 文件大小限制
- 认证密钥

### 客户端连接配置

客户端需要按照你的路径结构配置，SDK提供了多种便捷的配置方式：

#### 方式1：使用Builder进行详细配置（推荐）
```java
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.FileTransferClient;

ClientConfig config = ClientConfigBuilder.create()
    .serverAddr("localhost")
    .serverPort(49010)
    .contextPath("training") // 与服务端 context-path 匹配
    .auth("admin", "training-admin-secret-2024")
    .chunkSize(2 * 1024 * 1024L) // 2MB 分块
    .timeouts(120) // 统一设置超时时间为120秒
    .maxConcurrentTransfers(3) // 并发传输数
    .retry(5, 1000) // 5次重试，间隔1秒
    .build();

FileTransferClient client = new FileTransferClient(config);
```

#### 方式2：本地开发环境快速配置
```java
ClientConfig localConfig = ClientConfigBuilder.localConfig(
    "admin", "training-admin-secret-2024");
// 注意：需要手动设置context-path
localConfig.setContextPath("training");
localConfig.setServerPort(49010);
```

#### 方式3：使用便捷方法快速配置
```java
ClientConfig quickConfig = ClientConfigBuilder.quickConnect(
    "localhost", "admin", "training-admin-secret-2024");
quickConfig.setContextPath("training");
quickConfig.setServerPort(49010);
```

### 与现有用户系统集成

如果你希望与JeecG的用户系统集成，可以创建一个适配器：

```java
@Component
public class JeecgUserAdapter {
    
    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private FileTransferProperties properties;
    
    /**
     * 根据JeecG用户获取文件传输配置
     */
    public UserConfig getUserConfigByJeecgUser(String username) {
        SysUser user = sysUserService.getUserByName(username);
        if (user == null) {
            return null;
        }
        
        // 根据用户角色返回不同配置
        String role = getUserRole(user);
        return properties.getUserConfig(role);
    }
    
    private String getUserRole(SysUser user) {
        // 根据你的业务逻辑确定用户角色
        // 比如：admin、teacher、student等
        return "student"; // 示例
    }
}
```

## 🚦 功能特性

整合后你将获得以下功能：

### ✅ 核心功能
- **高性能传输** - 支持分块上传与多线程分块下载，充分利用网络带宽
- **断点续传** - 上传或下载中断后可从断点无缝续传，支持基于HTTP Range的下载
- **智能秒传** - 基于文件MD5进行秒传判断，通过物理文件校验确保文件一致性，支持跨用户秒传
- **多用户与权限** - 支持多用户隔离，可为不同用户配置独立的存储路径、密钥和权限
- **动态速率限制** - 可为每个用户独立设置上传和下载速率限制，并可在服务运行时动态调整
- **进度查询** - 实时传输进度监控

### ✅ 高级功能
- **数据库容错** - 即使数据库服务异常，系统仍能基于文件系统的元数据信息提供下载服务
- **智能清理机制** - 自动清理过期记录，永久保护所有成功传输记录以最大化秒传功能，智能清理失败记录
- **丰富的管理功能** - 提供API进行数据库健康检查、备份、以及从物理文件反向重建数据库
- **清理统计监控** - 详细的清理操作统计信息，包括清理次数、清理的记录数量等
- **API文档** - 自动生成接口文档
- **跨域支持** - Web前端集成

### ✅ 安全功能
- **无状态认证** - 基于 `HMAC-SHA256` 的无状态认证机制，安全且易于集成
- **角色权限控制** - 支持普通用户和管理员角色，精确控制接口访问权限
- **文件校验** - MD5完整性验证，确保文件传输的准确性
- **路径隔离** - 用户文件隔离存储，保证数据安全

## 🛠️ 高级配置

### 自定义数据源

如果你希望SDK使用你现有的MySQL数据库而不是SQLite：

```java
@Configuration
public class FileTransferDatabaseConfig {
    
    @Bean
    @Primary
    public DataSource fileTransferDataSource() {
        // 返回你的MySQL数据源
        return yourMySQLDataSource;
    }
}
```

**注意**: 需要手动创建对应的MySQL表结构。

### 自定义存储策略

```java
@Configuration
public class CustomFileStorageConfig {
    
    @Bean
    public FileStorageService customFileStorageService() {
        // 实现你的自定义存储逻辑
        // 比如存储到阿里云OSS、腾讯云COS等
        return new CustomFileStorageServiceImpl();
    }
}
```

### 监控集成

SDK提供监控服务，可以集成到你的监控系统：

```java
@Component
public class FileTransferMetrics {
    
    @Autowired
    private FileTransferMonitorService monitorService;
    
    @EventListener
    public void handleTransferCompleted(TransferCompletedEvent event) {
        // 集成到你的监控系统
        yourMetricsService.recordTransfer(event);
    }
}
```

## 📋 整合检查清单

### 基础整合
- [ ] 添加SDK依赖到pom.xml
- [ ] 添加file.transfer配置到application.yml
- [ ] 修改jeecg.shiro.excludeUrls，添加`/api/file/**`
- [ ] 如需管理功能，添加`/api/admin/**,/api/database/**`到excludeUrls
- [ ] 确认文件上传大小限制配置一致
- [ ] 创建文件存储目录并设置权限

### 功能验证
- [ ] 测试基础健康检查：`http://localhost:49010/training/api/file/health`
- [ ] 测试API文档：`http://localhost:49010/training/doc.html`
- [ ] 验证Shiro不会拦截文件传输API
- [ ] 测试客户端连接（注意context-path）

### 管理功能验证（如果启用）
- [ ] 测试管理员统计接口：`http://localhost:49010/training/api/admin/statistics`
- [ ] 测试数据库健康检查：`http://localhost:49010/training/api/database/health`
- [ ] 验证角色权限控制正常工作
- [ ] 测试清理功能配置和统计

### 高级功能验证
- [ ] 测试智能秒传功能
- [ ] 测试断点续传功能
- [ ] 测试数据库容错机制
- [ ] 验证清理机制不影响核心功能

## 🔍 常见问题

### Q1: SDK会影响我现有的JeecG配置吗？
**A**: 不会。SDK使用条件注解，只会在需要时生效，不会覆盖你现有的配置。

### Q2: 如何禁用SDK功能？
**A**: 设置 `file.transfer.server.enabled=false` 即可完全禁用。

### Q3: 可以修改API路径吗？
**A**: 可以通过实现自定义Controller或使用Spring的路径映射来调整。

### Q4: 如何处理大文件上传？
**A**: SDK自动处理分块上传，你只需要调整 `max-file-size` 和 `default-chunk-size` 配置。

### Q5: 支持集群部署吗？
**A**: 当前版本使用SQLite，建议单实例部署。如需集群支持，可以配置共享的MySQL数据库。

### Q6: Context Path对API有什么影响？
**A**: 所有文件传输API都会添加context-path前缀，客户端连接时需要包含完整路径。

### Q7: Shiro鉴权冲突怎么办？
**A**: 必须在shiro的excludeUrls中添加`/api/file/**`，如需管理功能还要添加`/api/admin/**,/api/database/**`，或者实现自定义认证逻辑。

### Q8: 如何使用管理功能？
**A**: 配置用户时设置`role: "admin"`，然后在Shiro中排除管理接口路径，即可访问统计、清理、数据库管理等功能。

### Q9: 清理机制会影响秒传功能吗？
**A**: 不会。清理机制采用智能策略，永久保护所有成功传输记录以最大化秒传功能，只清理失败和异常状态的记录。

### Q10: 数据库容错机制如何工作？
**A**: 系统会在每个文件目录下创建info.json元数据文件。当数据库不可用时，系统自动切换到基于文件系统的查找模式，确保下载服务不中断。

## 📞 技术支持

如果在整合过程中遇到问题，可以：

1. 检查日志输出中的 `com.sdesrd.filetransfer` 包下的日志
2. 访问健康检查端点：`http://localhost:49010/training/api/file/health`
3. 查看API文档：`http://localhost:49010/training/doc.html`
4. 检查Shiro配置是否正确排除了文件传输API
5. 如果使用管理功能，确认用户角色配置为`admin`
6. 测试数据库健康状态：`http://localhost:49010/training/api/database/health`
7. 查看清理统计信息：`http://localhost:49010/training/api/admin/cleanup/statistics`

---

## 🎯 完整示例

这里提供一个完整的整合示例项目结构，可以直接参考：

```java
// 你的主启动类
@SpringBootApplication
public class TrainingApplication {
    public static void main(String[] args) {
        SpringApplication.run(TrainingApplication.class, args);
    }
}

// 你的配置文件 application.yml
server:
  port: 49010
  servlet:
    context-path: /training

file:
  transfer:
    server:
      enabled: true
      users:
        admin:
          secret-key: "training-admin-secret-2024"
          storage-path: /home/<USER>/trainsaas/data/file-transfer/admin

jeecg:
  shiro:
    # 注意：由于统一使用context-path配置，这里需要包含完整路径
    excludeUrls: /sys/**,/jmreport/**,/login/**,/cas/login,/jeecg/**,/auth/**,/appdev/**,/docs/**,/ver_*,/**/category/getRandomBackground,/api/file/**,/api/admin/**,/api/database/**
```

启动后访问：
- 你的原有API：`http://localhost:49010/training/your-api`
- 文件传输API：`http://localhost:49010/training/api/file/*`
- 管理接口：`http://localhost:49010/training/api/admin/*`
- 数据库管理：`http://localhost:49010/training/api/database/*`
- API文档：`http://localhost:49010/training/doc.html`

## 🚀 独立服务端选项

如果不想集成到现有项目中，也可以使用独立的服务端：

```bash
# 编译独立服务端
cd file-transfer-server-standalone
mvn clean package

# 运行独立服务端
java -jar target/file-transfer-server-standalone-1.0.0.jar
```

独立服务端特性：
- **默认端口**: 49011
- **文件传输API**: `http://localhost:49011/api/file/*`
- **管理接口**: `http://localhost:49011/api/admin/*`
- **数据库管理**: `http://localhost:49011/api/database/*`
- **API文档**: `http://localhost:49011/doc.html`
- **健康检查**: `http://localhost:49011/actuator/health`

## 🔧 高级配置和功能

### 智能清理机制配置

SDK提供了智能清理机制，可以自动清理过期记录，同时保护核心功能：

```yaml
file:
  transfer:
    server:
      # 清理配置最佳实践
      cleanup-enabled: true
      cleanup-interval: 3600000              # 1小时清理一次
      record-expire-time: 172800000          # 48小时
      chunk-expire-time: 86400000            # 24小时（传输完成后及时清理）
      failed-record-retain-time: 259200000   # 3天（保留用于问题分析）
      max-batch-delete-size: 500             # 适中的批量大小
```

### 数据库容错机制

系统设计了完善的数据库容错机制：

1. **元数据冗余存储**: 每个文件都会在存储目录下创建`info.json`元数据文件
2. **三层查找机制**: 数据库 → 路径规则 → info.json文件
3. **自动重建机制**: 可通过`/api/database/rebuild`接口从磁盘重建数据库
4. **健康检查**: 定期检查数据库状态，自动切换到容错模式

### 角色权限管理

配置不同角色的用户：

```yaml
file:
  transfer:
    server:
      users:
        # 管理员用户
        admin:
          role: "admin"                 # 可访问所有接口
          secret-key: "admin-secret"
          # ... 其他配置

        # 普通用户
        user1:
          role: "user"                  # 只能访问文件传输接口
          secret-key: "user1-secret"
          # ... 其他配置
```

## 🚀 最新功能特性

### 智能秒传机制

SDK提供了两种级别的秒传检测：

1. **fileId + MD5 完全匹配秒传**: 客户端重复上传相同文件到相同fileId
2. **仅 MD5 匹配秒传（跨用户共享）**: 不同用户上传相同内容的文件

**重要特性**:
- 跨用户秒传检测，但保证数据隔离
- 物理文件复制到用户专属存储路径
- 清理系统保护所有成功记录，确保秒传长期有效

### 清理统计监控

通过管理接口可以监控清理操作：

```bash
# 获取清理统计信息
GET /api/admin/cleanup/statistics

# 手动触发清理操作
POST /api/admin/cleanup/manual

# 获取清理配置
GET /api/admin/cleanup/config
```

### 数据库管理功能

提供完整的数据库管理能力：

```bash
# 数据库健康检查
GET /api/database/health

# 创建数据库备份
POST /api/database/backup

# 列出所有备份
GET /api/database/backups

# 下载备份文件
GET /api/database/backup/download/{fileName}

# 从磁盘重建数据库
POST /api/database/rebuild
```

### 客户端SDK增强

新的客户端配置构建器提供更便捷的配置方式：

```java
// 高性能配置
ClientConfig config = ClientConfigBuilder.highPerformanceConfig(
    "api.example.com", "user", "secret");

// 生产环境配置
ClientConfig prodConfig = ClientConfigBuilder.productionConfig(
    "prod.example.com", 443, "user", "secret");

// 本地开发配置
ClientConfig localConfig = ClientConfigBuilder.localConfig(
    "user", "secret");
```

整合完成！🎉