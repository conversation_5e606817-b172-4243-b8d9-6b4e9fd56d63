@echo off
cd /d "D:\Data\gitdb\file-transfer-sdk\file-transfer-server-standalone"

echo 创建测试目录...
if exist "D:\Data\TEMP\test-deployment-fix" rmdir /s /q "D:\Data\TEMP\test-deployment-fix"
mkdir "D:\Data\TEMP\test-deployment-fix"

echo 解压分发包...
powershell -Command "Expand-Archive -Path '.\target\file-transfer-server-standalone-1.0.0-dist.zip' -DestinationPath 'D:\Data\TEMP\test-deployment-fix' -Force"

echo 切换到部署目录...
cd /d "D:\Data\TEMP\test-deployment-fix"

echo 列出部署目录内容...
dir

echo 测试启动脚本...
powershell -ExecutionPolicy Bypass -Command ".\bin\start-server.ps1 start -Background"

pause 